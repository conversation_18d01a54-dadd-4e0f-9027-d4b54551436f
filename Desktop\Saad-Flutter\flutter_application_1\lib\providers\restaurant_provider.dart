import 'package:flutter/foundation.dart';
import '../models/restaurant.dart';

class RestaurantProvider with ChangeNotifier {
  List<Restaurant> _restaurants = [];
  List<String> _categories = [];
  String _searchQuery = '';
  String _selectedCategory = '';
  bool _isLoading = false;

  List<Restaurant> get restaurants => _restaurants;
  List<String> get categories => _categories;
  String get searchQuery => _searchQuery;
  String get selectedCategory => _selectedCategory;
  bool get isLoading => _isLoading;

  List<Restaurant> get filteredRestaurants {
    var filtered = _restaurants;

    // تطبيق فلتر البحث
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((restaurant) =>
          restaurant.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          restaurant.description.toLowerCase().contains(_searchQuery.toLowerCase())).toList();
    }

    // تطبيق فلتر الفئة
    if (_selectedCategory.isNotEmpty) {
      filtered = filtered.where((restaurant) =>
          restaurant.categories.contains(_selectedCategory)).toList();
    }

    return filtered;
  }

  void setSearchQuery(String query) {
    _searchQuery = query;
    notifyListeners();
  }

  void setSelectedCategory(String category) {
    _selectedCategory = category;
    notifyListeners();
  }

  void clearFilters() {
    _searchQuery = '';
    _selectedCategory = '';
    notifyListeners();
  }

  Future<void> loadRestaurants() async {
    _isLoading = true;
    notifyListeners();

    try {
      // محاكاة تحميل البيانات من API
      await Future.delayed(Duration(seconds: 1));
      
      _restaurants = _getDummyRestaurants();
      _categories = _extractCategories();
    } catch (e) {
      print('خطأ في تحميل المطاعم: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Restaurant? getRestaurantById(String id) {
    try {
      return _restaurants.firstWhere((restaurant) => restaurant.id == id);
    } catch (e) {
      return null;
    }
  }

  List<String> _extractCategories() {
    Set<String> categorySet = {};
    for (var restaurant in _restaurants) {
      categorySet.addAll(restaurant.categories);
    }
    return categorySet.toList();
  }

  List<Restaurant> _getDummyRestaurants() {
    return [
      Restaurant(
        id: 'rest_1',
        name: 'مطعم البرجر الذهبي',
        description: 'أشهى أنواع البرجر والوجبات السريعة',
        imageUrl: 'https://images.unsplash.com/photo-1571091718767-18b5b1457add',
        rating: 4.5,
        deliveryTime: 25,
        deliveryFee: 8.0,
        categories: ['برجر', 'وجبات سريعة'],
        isOpen: true,
        address: 'شارع الملك فهد، الرياض',
        menu: [
          FoodItem(
            id: 'rest_1_item_1',
            name: 'برجر كلاسيك',
            description: 'برجر لحم بقري مع الخضار الطازجة',
            imageUrl: 'https://images.unsplash.com/photo-1568901346375-23c9450c58cd',
            price: 25.0,
            category: 'برجر',
            isAvailable: true,
            ingredients: ['لحم بقري', 'خس', 'طماطم', 'جبن'],
          ),
          FoodItem(
            id: 'rest_1_item_2',
            name: 'برجر دجاج مقرمش',
            description: 'قطعة دجاج مقرمشة مع صوص خاص',
            imageUrl: 'https://images.unsplash.com/photo-1606755962773-d324e2d53352',
            price: 22.0,
            category: 'برجر',
            isAvailable: true,
            ingredients: ['دجاج مقرمش', 'خس', 'مايونيز'],
          ),
        ],
      ),
      Restaurant(
        id: 'rest_2',
        name: 'بيتزا إيطاليا',
        description: 'بيتزا إيطالية أصيلة بأجود المكونات',
        imageUrl: 'https://images.unsplash.com/photo-1513104890138-7c749659a591',
        rating: 4.7,
        deliveryTime: 35,
        deliveryFee: 10.0,
        categories: ['بيتزا', 'إيطالي'],
        isOpen: true,
        address: 'حي العليا، الرياض',
        menu: [
          FoodItem(
            id: 'rest_2_item_1',
            name: 'بيتزا مارجريتا',
            description: 'بيتزا كلاسيكية بالطماطم والجبن والريحان',
            imageUrl: 'https://images.unsplash.com/photo-1604382354936-07c5d9983bd3',
            price: 35.0,
            category: 'بيتزا',
            isAvailable: true,
            ingredients: ['طماطم', 'موزاريلا', 'ريحان'],
          ),
        ],
      ),
      Restaurant(
        id: 'rest_3',
        name: 'مطعم الشاورما اللبناني',
        description: 'شاورما لبنانية أصيلة وأطباق شرقية',
        imageUrl: 'https://images.unsplash.com/photo-1529006557810-274b9b2fc783',
        rating: 4.3,
        deliveryTime: 20,
        deliveryFee: 6.0,
        categories: ['شاورما', 'لبناني', 'شرقي'],
        isOpen: true,
        address: 'شارع التحلية، جدة',
        menu: [
          FoodItem(
            id: 'rest_3_item_1',
            name: 'شاورما دجاج',
            description: 'شاورما دجاج بالثوم والخضار',
            imageUrl: 'https://images.unsplash.com/photo-1529006557810-274b9b2fc783',
            price: 18.0,
            category: 'شاورما',
            isAvailable: true,
            ingredients: ['دجاج', 'ثوم', 'خضار مشكلة'],
          ),
        ],
      ),
    ];
  }
}
