import 'package:flutter/foundation.dart';
import '../models/cart.dart';

class OrderProvider with ChangeNotifier {
  List<Order> _orders = [];
  bool _isLoading = false;

  List<Order> get orders => _orders;
  bool get isLoading => _isLoading;

  List<Order> get activeOrders {
    return _orders.where((order) => 
      order.status != OrderStatus.delivered && 
      order.status != OrderStatus.cancelled
    ).toList();
  }

  List<Order> get completedOrders {
    return _orders.where((order) => 
      order.status == OrderStatus.delivered || 
      order.status == OrderStatus.cancelled
    ).toList();
  }

  Future<String> placeOrder({
    required Cart cart,
    required String restaurantName,
    required String deliveryAddress,
    String? customerNotes,
  }) async {
    _isLoading = true;
    notifyListeners();

    try {
      // محاكاة إرسال الطلب إلى الخادم
      await Future.delayed(Duration(seconds: 2));

      final orderId = 'order_${DateTime.now().millisecondsSinceEpoch}';
      
      final order = Order(
        id: orderId,
        items: List.from(cart.items),
        total: cart.total,
        status: OrderStatus.pending,
        orderTime: DateTime.now(),
        restaurantId: cart.restaurantId!,
        restaurantName: restaurantName,
        deliveryAddress: deliveryAddress,
        customerNotes: customerNotes,
      );

      _orders.insert(0, order);
      
      // محاكاة تحديث حالة الطلب تلقائياً
      _simulateOrderProgress(orderId);
      
      return orderId;
    } catch (e) {
      throw Exception('فشل في إرسال الطلب: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  void _simulateOrderProgress(String orderId) {
    // محاكاة تقدم الطلب
    Timer.periodic(Duration(seconds: 30), (timer) {
      final orderIndex = _orders.indexWhere((order) => order.id == orderId);
      if (orderIndex == -1) {
        timer.cancel();
        return;
      }

      final currentOrder = _orders[orderIndex];
      OrderStatus nextStatus;

      switch (currentOrder.status) {
        case OrderStatus.pending:
          nextStatus = OrderStatus.confirmed;
          break;
        case OrderStatus.confirmed:
          nextStatus = OrderStatus.preparing;
          break;
        case OrderStatus.preparing:
          nextStatus = OrderStatus.onTheWay;
          break;
        case OrderStatus.onTheWay:
          nextStatus = OrderStatus.delivered;
          timer.cancel();
          break;
        default:
          timer.cancel();
          return;
      }

      updateOrderStatus(orderId, nextStatus);
    });
  }

  void updateOrderStatus(String orderId, OrderStatus newStatus) {
    final orderIndex = _orders.indexWhere((order) => order.id == orderId);
    if (orderIndex != -1) {
      _orders[orderIndex] = Order(
        id: _orders[orderIndex].id,
        items: _orders[orderIndex].items,
        total: _orders[orderIndex].total,
        status: newStatus,
        orderTime: _orders[orderIndex].orderTime,
        restaurantId: _orders[orderIndex].restaurantId,
        restaurantName: _orders[orderIndex].restaurantName,
        deliveryAddress: _orders[orderIndex].deliveryAddress,
        customerNotes: _orders[orderIndex].customerNotes,
        estimatedDeliveryTime: _orders[orderIndex].estimatedDeliveryTime,
      );
      notifyListeners();
    }
  }

  Order? getOrderById(String orderId) {
    try {
      return _orders.firstWhere((order) => order.id == orderId);
    } catch (e) {
      return null;
    }
  }

  void cancelOrder(String orderId) {
    updateOrderStatus(orderId, OrderStatus.cancelled);
  }

  Future<void> loadOrders() async {
    _isLoading = true;
    notifyListeners();

    try {
      // محاكاة تحميل الطلبات من الخادم
      await Future.delayed(Duration(seconds: 1));
      // في التطبيق الحقيقي، ستحمل الطلبات من قاعدة البيانات
    } catch (e) {
      print('خطأ في تحميل الطلبات: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
}

// إضافة Timer للمحاكاة
import 'dart:async';
