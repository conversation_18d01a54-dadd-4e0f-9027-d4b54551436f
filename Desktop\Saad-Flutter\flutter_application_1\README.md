# تطبيق توصيل الطعام 🍔

تطبيق Flutter متكامل لطلب الطعام من المطاعم المختلفة مع خدمة توصيل سريعة وموثوقة.

## الميزات الرئيسية ✨

### 🏠 الصفحة الرئيسية
- شريط بحث ذكي للعثور على المطاعم والوجبات
- فئات الطعام المختلفة (برجر، بيتزا، شاورما، إلخ)
- قائمة المطاعم مع التقييمات ووقت التوصيل
- تصميم جذاب مع صور عالية الجودة

### 🍽️ تفاصيل المطعم
- عرض شامل لمعلومات المطعم
- قائمة الطعام مع الأسعار والمكونات
- إمكانية تصفية الوجبات حسب الفئة
- إضافة الوجبات إلى السلة مع تحديد الكمية

### 🛒 سلة التسوق
- إدارة العناصر المختارة
- تعديل الكميات أو حذف العناصر
- حساب المجموع مع الضرائب ورسوم التوصيل
- واجهة سهلة الاستخدام

### 📋 إدارة الطلبات
- تتبع الطلبات الحالية والسابقة
- عرض تفاصيل كل طلب
- تحديث حالة الطلب في الوقت الفعلي
- إمكانية إلغاء الطلبات

### 👤 الملف الشخصي
- إدارة المعلومات الشخصية
- عناوين التوصيل المحفوظة
- إعدادات التطبيق
- المساعدة والدعم

## التقنيات المستخدمة 🛠️

- **Flutter**: إطار العمل الأساسي
- **Provider**: إدارة الحالة
- **Cached Network Image**: تحميل وتخزين الصور
- **HTTP**: التواصل مع الخوادم
- **Material Design**: نظام التصميم

## هيكل المشروع 📁

```
lib/
├── main.dart                 # نقطة البداية
├── models/                   # نماذج البيانات
│   ├── restaurant.dart
│   └── cart.dart
├── providers/                # إدارة الحالة
│   ├── cart_provider.dart
│   ├── restaurant_provider.dart
│   └── order_provider.dart
├── pages/                    # صفحات التطبيق
│   ├── main_navigation.dart
│   ├── home_page.dart
│   ├── restaurant_detail_page.dart
│   ├── cart_page.dart
│   ├── checkout_page.dart
│   ├── orders_page.dart
│   └── profile_page.dart
├── widgets/                  # المكونات القابلة لإعادة الاستخدام
│   ├── search_bar.dart
│   ├── category_list.dart
│   ├── restaurant_card.dart
│   ├── food_item_card.dart
│   ├── cart_item_card.dart
│   └── order_card.dart
└── utils/                    # الأدوات المساعدة
    └── app_colors.dart
```

## كيفية التشغيل 🚀

1. **تأكد من تثبيت Flutter**:
   ```bash
   flutter doctor
   ```

2. **تحميل التبعيات**:
   ```bash
   flutter pub get
   ```

3. **تشغيل التطبيق**:
   ```bash
   flutter run
   ```

## المنصات المدعومة 📱

- ✅ Android
- ✅ iOS
- ✅ Web
- ✅ Windows
- ✅ macOS
- ✅ Linux

## لقطات الشاشة 📸

التطبيق يحتوي على واجهات جميلة ومتجاوبة مع:
- تصميم عربي أصيل
- ألوان متناسقة
- تجربة مستخدم سلسة
- دعم كامل للغة العربية

## المساهمة 🤝

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إضافة التحسينات
4. إرسال Pull Request

## الترخيص 📄

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## التواصل 📧

للاستفسارات والدعم، يرجى التواصل معنا عبر:
- البريد الإلكتروني: <EMAIL>
- الموقع الإلكتروني: www.fooddelivery.com

---

**تم التطوير بـ ❤️ باستخدام Flutter**
