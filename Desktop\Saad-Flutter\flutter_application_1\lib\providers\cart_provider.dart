import 'package:flutter/foundation.dart';
import '../models/cart.dart';
import '../models/restaurant.dart';

class CartProvider with ChangeNotifier {
  Cart _cart = Cart();

  Cart get cart => _cart;

  void addItem(FoodItem foodItem, {String? specialInstructions}) {
    // التحقق من أن العنصر من نفس المطعم
    if (_cart.restaurantId != null && _cart.restaurantId != foodItem.id.split('_')[0]) {
      // إذا كان من مطعم مختلف، اسأل المستخدم
      clearCart();
    }

    // البحث عن العنصر في السلة
    final existingItemIndex = _cart.items.indexWhere(
      (item) => item.foodItem.id == foodItem.id && 
                item.specialInstructions == specialInstructions,
    );

    if (existingItemIndex >= 0) {
      // زيادة الكمية إذا كان العنصر موجود
      _cart.items[existingItemIndex].quantity++;
    } else {
      // إضافة عنصر جديد
      _cart = _cart.copyWith(
        items: [..._cart.items, CartItem(
          foodItem: foodItem,
          specialInstructions: specialInstructions,
        )],
        restaurantId: _cart.restaurantId ?? foodItem.id.split('_')[0],
      );
    }
    notifyListeners();
  }

  void removeItem(String foodItemId) {
    _cart = _cart.copyWith(
      items: _cart.items.where((item) => item.foodItem.id != foodItemId).toList(),
    );
    
    // إذا أصبحت السلة فارغة، امسح معرف المطعم
    if (_cart.items.isEmpty) {
      _cart = _cart.copyWith(restaurantId: null);
    }
    
    notifyListeners();
  }

  void updateQuantity(String foodItemId, int quantity) {
    if (quantity <= 0) {
      removeItem(foodItemId);
      return;
    }

    final itemIndex = _cart.items.indexWhere(
      (item) => item.foodItem.id == foodItemId,
    );

    if (itemIndex >= 0) {
      _cart.items[itemIndex].quantity = quantity;
      notifyListeners();
    }
  }

  void clearCart() {
    _cart = Cart();
    notifyListeners();
  }

  int getItemQuantity(String foodItemId) {
    final item = _cart.items.firstWhere(
      (item) => item.foodItem.id == foodItemId,
      orElse: () => CartItem(foodItem: FoodItem(
        id: '',
        name: '',
        description: '',
        imageUrl: '',
        price: 0,
        category: '',
        isAvailable: false,
      ), quantity: 0),
    );
    return item.quantity;
  }

  bool isItemInCart(String foodItemId) {
    return _cart.items.any((item) => item.foodItem.id == foodItemId);
  }
}
