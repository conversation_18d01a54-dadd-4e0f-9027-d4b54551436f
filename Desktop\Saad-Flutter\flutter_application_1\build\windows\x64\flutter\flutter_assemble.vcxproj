﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Profile|x64">
      <Configuration>Profile</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{68D4A030-AC5A-3B93-9DCB-5603F8A3493F}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>flutter_assemble</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\build\windows\x64\CMakeFiles\2223bc3898b3b60dd96d2a7f71e3ab12\flutter_windows.dll.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Generating C:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/windows/flutter/ephemeral/flutter_windows.dll, C:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/windows/flutter/ephemeral/flutter_export.h, C:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/windows/flutter/ephemeral/flutter_windows.h, C:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/windows/flutter/ephemeral/flutter_messenger.h, C:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/windows/flutter/ephemeral/flutter_plugin_registrar.h, C:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/windows/flutter/ephemeral/flutter_texture_registrar.h, C:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/windows/flutter/ephemeral/cpp_client_wrapper/core_implementations.cc, C:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/windows/flutter/ephemeral/cpp_client_wrapper/standard_codec.cc, C:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/windows/flutter/ephemeral/cpp_client_wrapper/plugin_registrar.cc, C:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/windows/flutter/ephemeral/cpp_client_wrapper/flutter_engine.cc, C:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/windows/flutter/ephemeral/cpp_client_wrapper/flutter_view_controller.cc, _phony_</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=C:\flutter PROJECT_DIR=C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1 FLUTTER_ROOT=C:\flutter FLUTTER_EPHEMERAL_DIR=C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral PROJECT_DIR=C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1 FLUTTER_TARGET=C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\lib\main.dart DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzIuNw==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049ZDdiNTIzYjM1Ng==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049MzlkNmQ2ZTY5OQ==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My44LjE= DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false PACKAGE_CONFIG=C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\.dart_tool\package_config.json C:/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\flutter_windows.dll;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\flutter_export.h;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\flutter_windows.h;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\flutter_messenger.h;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\flutter_plugin_registrar.h;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\flutter_texture_registrar.h;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\cpp_client_wrapper\core_implementations.cc;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\cpp_client_wrapper\standard_codec.cc;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\cpp_client_wrapper\plugin_registrar.cc;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\cpp_client_wrapper\flutter_engine.cc;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\cpp_client_wrapper\flutter_view_controller.cc;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\build\windows\x64\flutter\_phony_</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</VerifyInputsAndOutputsExist>
      <Message Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">Generating C:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/windows/flutter/ephemeral/flutter_windows.dll, C:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/windows/flutter/ephemeral/flutter_export.h, C:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/windows/flutter/ephemeral/flutter_windows.h, C:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/windows/flutter/ephemeral/flutter_messenger.h, C:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/windows/flutter/ephemeral/flutter_plugin_registrar.h, C:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/windows/flutter/ephemeral/flutter_texture_registrar.h, C:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/windows/flutter/ephemeral/cpp_client_wrapper/core_implementations.cc, C:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/windows/flutter/ephemeral/cpp_client_wrapper/standard_codec.cc, C:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/windows/flutter/ephemeral/cpp_client_wrapper/plugin_registrar.cc, C:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/windows/flutter/ephemeral/cpp_client_wrapper/flutter_engine.cc, C:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/windows/flutter/ephemeral/cpp_client_wrapper/flutter_view_controller.cc, _phony_</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=C:\flutter PROJECT_DIR=C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1 FLUTTER_ROOT=C:\flutter FLUTTER_EPHEMERAL_DIR=C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral PROJECT_DIR=C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1 FLUTTER_TARGET=C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\lib\main.dart DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzIuNw==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049ZDdiNTIzYjM1Ng==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049MzlkNmQ2ZTY5OQ==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My44LjE= DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false PACKAGE_CONFIG=C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\.dart_tool\package_config.json C:/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Profile
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\flutter_windows.dll;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\flutter_export.h;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\flutter_windows.h;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\flutter_messenger.h;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\flutter_plugin_registrar.h;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\flutter_texture_registrar.h;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\cpp_client_wrapper\core_implementations.cc;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\cpp_client_wrapper\standard_codec.cc;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\cpp_client_wrapper\plugin_registrar.cc;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\cpp_client_wrapper\flutter_engine.cc;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\cpp_client_wrapper\flutter_view_controller.cc;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\build\windows\x64\flutter\_phony_</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</VerifyInputsAndOutputsExist>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Generating C:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/windows/flutter/ephemeral/flutter_windows.dll, C:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/windows/flutter/ephemeral/flutter_export.h, C:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/windows/flutter/ephemeral/flutter_windows.h, C:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/windows/flutter/ephemeral/flutter_messenger.h, C:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/windows/flutter/ephemeral/flutter_plugin_registrar.h, C:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/windows/flutter/ephemeral/flutter_texture_registrar.h, C:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/windows/flutter/ephemeral/cpp_client_wrapper/core_implementations.cc, C:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/windows/flutter/ephemeral/cpp_client_wrapper/standard_codec.cc, C:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/windows/flutter/ephemeral/cpp_client_wrapper/plugin_registrar.cc, C:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/windows/flutter/ephemeral/cpp_client_wrapper/flutter_engine.cc, C:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/windows/flutter/ephemeral/cpp_client_wrapper/flutter_view_controller.cc, _phony_</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=C:\flutter PROJECT_DIR=C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1 FLUTTER_ROOT=C:\flutter FLUTTER_EPHEMERAL_DIR=C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral PROJECT_DIR=C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1 FLUTTER_TARGET=C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\lib\main.dart DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzIuNw==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049ZDdiNTIzYjM1Ng==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049MzlkNmQ2ZTY5OQ==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My44LjE= DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false PACKAGE_CONFIG=C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\.dart_tool\package_config.json C:/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\flutter_windows.dll;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\flutter_export.h;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\flutter_windows.h;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\flutter_messenger.h;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\flutter_plugin_registrar.h;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\flutter_texture_registrar.h;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\cpp_client_wrapper\core_implementations.cc;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\cpp_client_wrapper\standard_codec.cc;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\cpp_client_wrapper\plugin_registrar.cc;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\cpp_client_wrapper\flutter_engine.cc;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\cpp_client_wrapper\flutter_view_controller.cc;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\build\windows\x64\flutter\_phony_</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</VerifyInputsAndOutputsExist>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\build\windows\x64\CMakeFiles\451b52951cb26e90e1bb3a81c55d9f8e\flutter_assemble.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\flutter_windows.dll;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\flutter_export.h;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\flutter_windows.h;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\flutter_messenger.h;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\flutter_plugin_registrar.h;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\flutter_texture_registrar.h;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\cpp_client_wrapper\core_implementations.cc;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\cpp_client_wrapper\standard_codec.cc;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\cpp_client_wrapper\plugin_registrar.cc;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\cpp_client_wrapper\flutter_engine.cc;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\cpp_client_wrapper\flutter_view_controller.cc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\build\windows\x64\flutter\CMakeFiles\flutter_assemble</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</VerifyInputsAndOutputsExist>
      <Message Condition="'$(Configuration)|$(Platform)'=='Profile|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">setlocal
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\flutter_windows.dll;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\flutter_export.h;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\flutter_windows.h;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\flutter_messenger.h;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\flutter_plugin_registrar.h;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\flutter_texture_registrar.h;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\cpp_client_wrapper\core_implementations.cc;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\cpp_client_wrapper\standard_codec.cc;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\cpp_client_wrapper\plugin_registrar.cc;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\cpp_client_wrapper\flutter_engine.cc;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\cpp_client_wrapper\flutter_view_controller.cc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\build\windows\x64\flutter\CMakeFiles\flutter_assemble</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</VerifyInputsAndOutputsExist>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\flutter_windows.dll;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\flutter_export.h;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\flutter_windows.h;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\flutter_messenger.h;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\flutter_plugin_registrar.h;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\flutter_texture_registrar.h;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\cpp_client_wrapper\core_implementations.cc;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\cpp_client_wrapper\standard_codec.cc;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\cpp_client_wrapper\plugin_registrar.cc;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\cpp_client_wrapper\flutter_engine.cc;C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\cpp_client_wrapper\flutter_view_controller.cc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\build\windows\x64\flutter\CMakeFiles\flutter_assemble</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</VerifyInputsAndOutputsExist>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule C:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/windows/flutter/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/windows -BC:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/build/windows/x64 --check-stamp-file C:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\generated_config.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\build\windows\x64\flutter\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">Building Custom Rule C:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/windows/flutter/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/windows -BC:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/build/windows/x64 --check-stamp-file C:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\generated_config.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\build\windows\x64\flutter\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule C:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/windows/flutter/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/windows -BC:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/build/windows/x64 --check-stamp-file C:/Users/<USER>/Desktop/Saad-Flutter/flutter_application_1/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\windows\flutter\ephemeral\generated_config.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\build\windows\x64\flutter\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <None Include="C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\build\windows\x64\flutter\CMakeFiles\flutter_assemble">
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="C:\Users\<USER>\Desktop\Saad-Flutter\flutter_application_1\build\windows\x64\ZERO_CHECK.vcxproj">
      <Project>{17ABDC18-953A-348C-AC6C-A2AB1A6B3B64}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>