import 'restaurant.dart';

class CartItem {
  final FoodItem foodItem;
  int quantity;
  final String? specialInstructions;

  CartItem({
    required this.foodItem,
    this.quantity = 1,
    this.specialInstructions,
  });

  double get totalPrice => foodItem.price * quantity;

  Map<String, dynamic> toJson() {
    return {
      'foodItem': foodItem.toJson(),
      'quantity': quantity,
      'specialInstructions': specialInstructions,
    };
  }

  factory CartItem.fromJson(Map<String, dynamic> json) {
    return CartItem(
      foodItem: FoodItem.fromJson(json['foodItem']),
      quantity: json['quantity'] ?? 1,
      specialInstructions: json['specialInstructions'],
    );
  }
}

class Cart {
  final List<CartItem> items;
  final String? restaurantId;

  Cart({
    this.items = const [],
    this.restaurantId,
  });

  double get subtotal {
    return items.fold(0.0, (sum, item) => sum + item.totalPrice);
  }

  double get deliveryFee => 5.0; // رسوم توصيل ثابتة

  double get tax => subtotal * 0.15; // ضريبة 15%

  double get total => subtotal + deliveryFee + tax;

  int get itemCount {
    return items.fold(0, (sum, item) => sum + item.quantity);
  }

  bool get isEmpty => items.isEmpty;

  Cart copyWith({
    List<CartItem>? items,
    String? restaurantId,
  }) {
    return Cart(
      items: items ?? this.items,
      restaurantId: restaurantId ?? this.restaurantId,
    );
  }
}

enum OrderStatus {
  pending,
  confirmed,
  preparing,
  onTheWay,
  delivered,
  cancelled,
}

class Order {
  final String id;
  final List<CartItem> items;
  final double total;
  final OrderStatus status;
  final DateTime orderTime;
  final String restaurantId;
  final String restaurantName;
  final String deliveryAddress;
  final String? customerNotes;
  final int estimatedDeliveryTime;

  Order({
    required this.id,
    required this.items,
    required this.total,
    required this.status,
    required this.orderTime,
    required this.restaurantId,
    required this.restaurantName,
    required this.deliveryAddress,
    this.customerNotes,
    this.estimatedDeliveryTime = 30,
  });

  String get statusText {
    switch (status) {
      case OrderStatus.pending:
        return 'في انتظار التأكيد';
      case OrderStatus.confirmed:
        return 'تم تأكيد الطلب';
      case OrderStatus.preparing:
        return 'جاري التحضير';
      case OrderStatus.onTheWay:
        return 'في الطريق';
      case OrderStatus.delivered:
        return 'تم التوصيل';
      case OrderStatus.cancelled:
        return 'ملغي';
    }
  }

  factory Order.fromJson(Map<String, dynamic> json) {
    return Order(
      id: json['id'] ?? '',
      items: (json['items'] as List<dynamic>?)
              ?.map((item) => CartItem.fromJson(item))
              .toList() ??
          [],
      total: (json['total'] ?? 0.0).toDouble(),
      status: OrderStatus.values[json['status'] ?? 0],
      orderTime: DateTime.parse(json['orderTime'] ?? DateTime.now().toIso8601String()),
      restaurantId: json['restaurantId'] ?? '',
      restaurantName: json['restaurantName'] ?? '',
      deliveryAddress: json['deliveryAddress'] ?? '',
      customerNotes: json['customerNotes'],
      estimatedDeliveryTime: json['estimatedDeliveryTime'] ?? 30,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'items': items.map((item) => item.toJson()).toList(),
      'total': total,
      'status': status.index,
      'orderTime': orderTime.toIso8601String(),
      'restaurantId': restaurantId,
      'restaurantName': restaurantName,
      'deliveryAddress': deliveryAddress,
      'customerNotes': customerNotes,
      'estimatedDeliveryTime': estimatedDeliveryTime,
    };
  }
}
