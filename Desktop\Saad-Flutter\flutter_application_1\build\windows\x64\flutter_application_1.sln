﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{3C39900A-49CD-3D5F-A829-7B6A316FCC5D}"
	ProjectSection(ProjectDependencies) = postProject
		{17ABDC18-953A-348C-AC6C-A2AB1A6B3B64} = {17ABDC18-953A-348C-AC6C-A2AB1A6B3B64}
		{3EED6579-0B8B-3B96-96E5-E2C01963BE3D} = {3EED6579-0B8B-3B96-96E5-E2C01963BE3D}
		{7A6E066D-CF38-33BE-BCC0-865C05AED1C3} = {7A6E066D-CF38-33BE-BCC0-865C05AED1C3}
		{AAD9E3C1-F204-39F4-B38F-486D9F185227} = {AAD9E3C1-F204-39F4-B38F-486D9F185227}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{354C1D4C-5437-3701-92E2-A2E58BCE75E6}"
	ProjectSection(ProjectDependencies) = postProject
		{3C39900A-49CD-3D5F-A829-7B6A316FCC5D} = {3C39900A-49CD-3D5F-A829-7B6A316FCC5D}
		{17ABDC18-953A-348C-AC6C-A2AB1A6B3B64} = {17ABDC18-953A-348C-AC6C-A2AB1A6B3B64}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{17ABDC18-953A-348C-AC6C-A2AB1A6B3B64}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_application_1", "runner\flutter_application_1.vcxproj", "{3EED6579-0B8B-3B96-96E5-E2C01963BE3D}"
	ProjectSection(ProjectDependencies) = postProject
		{17ABDC18-953A-348C-AC6C-A2AB1A6B3B64} = {17ABDC18-953A-348C-AC6C-A2AB1A6B3B64}
		{68D4A030-AC5A-3B93-9DCB-5603F8A3493F} = {68D4A030-AC5A-3B93-9DCB-5603F8A3493F}
		{7A6E066D-CF38-33BE-BCC0-865C05AED1C3} = {7A6E066D-CF38-33BE-BCC0-865C05AED1C3}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_assemble", "flutter\flutter_assemble.vcxproj", "{68D4A030-AC5A-3B93-9DCB-5603F8A3493F}"
	ProjectSection(ProjectDependencies) = postProject
		{17ABDC18-953A-348C-AC6C-A2AB1A6B3B64} = {17ABDC18-953A-348C-AC6C-A2AB1A6B3B64}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_wrapper_app", "flutter\flutter_wrapper_app.vcxproj", "{7A6E066D-CF38-33BE-BCC0-865C05AED1C3}"
	ProjectSection(ProjectDependencies) = postProject
		{17ABDC18-953A-348C-AC6C-A2AB1A6B3B64} = {17ABDC18-953A-348C-AC6C-A2AB1A6B3B64}
		{68D4A030-AC5A-3B93-9DCB-5603F8A3493F} = {68D4A030-AC5A-3B93-9DCB-5603F8A3493F}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_wrapper_plugin", "flutter\flutter_wrapper_plugin.vcxproj", "{AAD9E3C1-F204-39F4-B38F-486D9F185227}"
	ProjectSection(ProjectDependencies) = postProject
		{17ABDC18-953A-348C-AC6C-A2AB1A6B3B64} = {17ABDC18-953A-348C-AC6C-A2AB1A6B3B64}
		{68D4A030-AC5A-3B93-9DCB-5603F8A3493F} = {68D4A030-AC5A-3B93-9DCB-5603F8A3493F}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Profile|x64 = Profile|x64
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{3C39900A-49CD-3D5F-A829-7B6A316FCC5D}.Debug|x64.ActiveCfg = Debug|x64
		{3C39900A-49CD-3D5F-A829-7B6A316FCC5D}.Debug|x64.Build.0 = Debug|x64
		{3C39900A-49CD-3D5F-A829-7B6A316FCC5D}.Profile|x64.ActiveCfg = Profile|x64
		{3C39900A-49CD-3D5F-A829-7B6A316FCC5D}.Profile|x64.Build.0 = Profile|x64
		{3C39900A-49CD-3D5F-A829-7B6A316FCC5D}.Release|x64.ActiveCfg = Release|x64
		{3C39900A-49CD-3D5F-A829-7B6A316FCC5D}.Release|x64.Build.0 = Release|x64
		{354C1D4C-5437-3701-92E2-A2E58BCE75E6}.Debug|x64.ActiveCfg = Debug|x64
		{354C1D4C-5437-3701-92E2-A2E58BCE75E6}.Debug|x64.Build.0 = Debug|x64
		{354C1D4C-5437-3701-92E2-A2E58BCE75E6}.Profile|x64.ActiveCfg = Profile|x64
		{354C1D4C-5437-3701-92E2-A2E58BCE75E6}.Profile|x64.Build.0 = Profile|x64
		{354C1D4C-5437-3701-92E2-A2E58BCE75E6}.Release|x64.ActiveCfg = Release|x64
		{354C1D4C-5437-3701-92E2-A2E58BCE75E6}.Release|x64.Build.0 = Release|x64
		{17ABDC18-953A-348C-AC6C-A2AB1A6B3B64}.Debug|x64.ActiveCfg = Debug|x64
		{17ABDC18-953A-348C-AC6C-A2AB1A6B3B64}.Debug|x64.Build.0 = Debug|x64
		{17ABDC18-953A-348C-AC6C-A2AB1A6B3B64}.Profile|x64.ActiveCfg = Profile|x64
		{17ABDC18-953A-348C-AC6C-A2AB1A6B3B64}.Profile|x64.Build.0 = Profile|x64
		{17ABDC18-953A-348C-AC6C-A2AB1A6B3B64}.Release|x64.ActiveCfg = Release|x64
		{17ABDC18-953A-348C-AC6C-A2AB1A6B3B64}.Release|x64.Build.0 = Release|x64
		{3EED6579-0B8B-3B96-96E5-E2C01963BE3D}.Debug|x64.ActiveCfg = Debug|x64
		{3EED6579-0B8B-3B96-96E5-E2C01963BE3D}.Debug|x64.Build.0 = Debug|x64
		{3EED6579-0B8B-3B96-96E5-E2C01963BE3D}.Profile|x64.ActiveCfg = Profile|x64
		{3EED6579-0B8B-3B96-96E5-E2C01963BE3D}.Profile|x64.Build.0 = Profile|x64
		{3EED6579-0B8B-3B96-96E5-E2C01963BE3D}.Release|x64.ActiveCfg = Release|x64
		{3EED6579-0B8B-3B96-96E5-E2C01963BE3D}.Release|x64.Build.0 = Release|x64
		{68D4A030-AC5A-3B93-9DCB-5603F8A3493F}.Debug|x64.ActiveCfg = Debug|x64
		{68D4A030-AC5A-3B93-9DCB-5603F8A3493F}.Debug|x64.Build.0 = Debug|x64
		{68D4A030-AC5A-3B93-9DCB-5603F8A3493F}.Profile|x64.ActiveCfg = Profile|x64
		{68D4A030-AC5A-3B93-9DCB-5603F8A3493F}.Profile|x64.Build.0 = Profile|x64
		{68D4A030-AC5A-3B93-9DCB-5603F8A3493F}.Release|x64.ActiveCfg = Release|x64
		{68D4A030-AC5A-3B93-9DCB-5603F8A3493F}.Release|x64.Build.0 = Release|x64
		{7A6E066D-CF38-33BE-BCC0-865C05AED1C3}.Debug|x64.ActiveCfg = Debug|x64
		{7A6E066D-CF38-33BE-BCC0-865C05AED1C3}.Debug|x64.Build.0 = Debug|x64
		{7A6E066D-CF38-33BE-BCC0-865C05AED1C3}.Profile|x64.ActiveCfg = Profile|x64
		{7A6E066D-CF38-33BE-BCC0-865C05AED1C3}.Profile|x64.Build.0 = Profile|x64
		{7A6E066D-CF38-33BE-BCC0-865C05AED1C3}.Release|x64.ActiveCfg = Release|x64
		{7A6E066D-CF38-33BE-BCC0-865C05AED1C3}.Release|x64.Build.0 = Release|x64
		{AAD9E3C1-F204-39F4-B38F-486D9F185227}.Debug|x64.ActiveCfg = Debug|x64
		{AAD9E3C1-F204-39F4-B38F-486D9F185227}.Debug|x64.Build.0 = Debug|x64
		{AAD9E3C1-F204-39F4-B38F-486D9F185227}.Profile|x64.ActiveCfg = Profile|x64
		{AAD9E3C1-F204-39F4-B38F-486D9F185227}.Profile|x64.Build.0 = Profile|x64
		{AAD9E3C1-F204-39F4-B38F-486D9F185227}.Release|x64.ActiveCfg = Release|x64
		{AAD9E3C1-F204-39F4-B38F-486D9F185227}.Release|x64.Build.0 = Release|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {BCE62A02-54B8-37F1-9F8E-012B74135FEC}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
